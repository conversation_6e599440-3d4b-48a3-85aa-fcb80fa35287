namespace Basket.Core.Specs
{
    public class BasketSpecParams
    {
        private const int MaxPageSize = 70;
        private int _pageSize = 10;

        public int PageIndex { get; set; } = 0;
        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value > MaxPageSize) ? MaxPageSize : value;
        }
        public string? UserName { get; set; }
        public string? Sort { get; set; }
        public string? Search { get; set; }
    }
}
