using Microsoft.Extensions.Configuration;
using StackExchange.Redis;

namespace Basket.Infrastructure.Data
{
    public class BasketContext : IBasketContext
    {
        public IDatabase Redis { get; }

        public BasketContext(IConfiguration configuration)
        {
            var connectionString = configuration.GetValue<string>("CacheSettings:ConnectionString");
            var connection = ConnectionMultiplexer.Connect(connectionString);
            Redis = connection.GetDatabase();
        }
    }
}
