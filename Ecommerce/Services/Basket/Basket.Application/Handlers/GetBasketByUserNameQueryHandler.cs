using Basket.Application.Mappers;
using Basket.Application.Queries;
using Basket.Application.Response;
using Basket.Core.Entities;
using Basket.Core.Repositories;
using MediatR;

namespace Basket.Application.Handlers
{
    public class GetBasketByUserNameQueryHandler : IRequestHandler<GetBasketByUserNameQuery, ShoppingCartResponse>
    {
        private readonly IBasketRepository _basketRepository;
        
        public GetBasketByUserNameQueryHandler(IBasketRepository basketRepository)
        {
            _basketRepository = basketRepository;
        }
        
        public async Task<ShoppingCartResponse> Handle(GetBasketByUserNameQuery request, CancellationToken cancellationToken)
        {
            var basket = await _basketRepository.GetBasket(request.UserName);
            var basketResponse = BasketMapper.Mapper.Map<ShoppingCart, ShoppingCartResponse>(basket);
            return basketResponse;
        }
    }
}
