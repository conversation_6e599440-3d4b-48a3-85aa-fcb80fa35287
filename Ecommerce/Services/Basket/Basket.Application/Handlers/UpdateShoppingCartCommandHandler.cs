using Basket.Application.Commands;
using Basket.Application.Mappers;
using Basket.Application.Response;
using Basket.Core.Entities;
using Basket.Core.Repositories;
using MediatR;

namespace Basket.Application.Handlers
{
    public class UpdateShoppingCartCommandHandler : IRequestHandler<UpdateShoppingCartCommand, ShoppingCartResponse>
    {
        private readonly IBasketRepository _basketRepository;
        
        public UpdateShoppingCartCommandHandler(IBasketRepository basketRepository)
        {
            _basketRepository = basketRepository;
        }
        
        public async Task<ShoppingCartResponse> Handle(UpdateShoppingCartCommand request, CancellationToken cancellationToken)
        {
            var basketEntity = BasketMapper.Mapper.Map<UpdateShoppingCartCommand, ShoppingCart>(request);
            if (basketEntity == null)
            {
                throw new ApplicationException("Shopping cart is null");
            }
            var basket = await _basketRepository.UpdateBasket(basketEntity);
            var basketResponse = BasketMapper.Mapper.Map<ShoppingCart, ShoppingCartResponse>(basket);
            return basketResponse;
        }
    }
}
