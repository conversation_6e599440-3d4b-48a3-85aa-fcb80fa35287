using Basket.Application.Commands;
using Basket.Application.Mappers;
using Basket.Application.Response;
using Basket.Core.Entities;
using Basket.Core.Repositories;
using MediatR;

namespace Basket.Application.Handlers
{
    public class CreateShoppingCartCommandHandler : IRequestHandler<CreateShoppingCartCommand, ShoppingCartResponse>
    {
        private readonly IBasketRepository _basketRepository;
        
        public CreateShoppingCartCommandHandler(IBasketRepository basketRepository)
        {
            _basketRepository = basketRepository;
        }
        
        public async Task<ShoppingCartResponse> Handle(CreateShoppingCartCommand request, CancellationToken cancellationToken)
        {
            var basketEntity = BasketMapper.Mapper.Map<CreateShoppingCartCommand, ShoppingCart>(request);
            if (basketEntity == null)
            {
                throw new ApplicationException("Shopping cart is null");
            }
            var basket = await _basketRepository.UpdateBasket(basketEntity);
            var basketResponse = BasketMapper.Mapper.Map<ShoppingCart, ShoppingCartResponse>(basket);
            return basketResponse;
        }
    }
}
