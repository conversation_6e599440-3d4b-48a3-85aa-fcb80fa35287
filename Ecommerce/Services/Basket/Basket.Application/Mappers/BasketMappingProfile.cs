using AutoMapper;
using Basket.Application.Commands;
using Basket.Application.Response;
using Basket.Core.Entities;
using Basket.Core.Specs;

namespace Basket.Application.Mappers
{
    public class BasketMappingProfile : Profile
    {
        public BasketMappingProfile()
        {
            CreateMap<ShoppingCart, ShoppingCartResponse>().ReverseMap();
            CreateMap<ShoppingCartItem, ShoppingCartItemResponse>().ReverseMap();
            CreateMap<ShoppingCart, CreateShoppingCartCommand>().ReverseMap();
            CreateMap<ShoppingCart, UpdateShoppingCartCommand>().ReverseMap();
            CreateMap<Pagination<ShoppingCart>, Pagination<ShoppingCartResponse>>().ReverseMap();
        }
    }
}
