{"version": 2, "dgSpecHash": "t2mU6SUzJps=", "success": true, "projectFilePath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Basket/Basket.API/Basket.API.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/aspnetcore.healthchecks.redis/8.0.1/aspnetcore.healthchecks.redis.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/automapper/13.0.1/automapper.13.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/mediatr/12.3.0/mediatr.12.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/mediatr.contracts/2.0.1/mediatr.contracts.2.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.apidescription.server/6.0.5/microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/8.0.0/microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/8.0.0/microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.abstractions/8.0.0/microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.healthchecks/8.0.0/microsoft.extensions.diagnostics.healthchecks.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.healthchecks.abstractions/8.0.0/microsoft.extensions.diagnostics.healthchecks.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/8.0.0/microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting.abstractions/8.0.0/microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/8.0.0/microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/8.0.0/microsoft.extensions.options.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/8.0.0/microsoft.extensions.primitives.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.openapi/1.6.14/microsoft.openapi.1.6.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.visualstudio.azure.containers.tools.targets/1.22.1-preview.1/microsoft.visualstudio.azure.containers.tools.targets.1.22.1-preview.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/pipelines.sockets.unofficial/2.2.8/pipelines.sockets.unofficial.2.2.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/stackexchange.redis/2.7.33/stackexchange.redis.2.7.33.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore/6.6.2/swashbuckle.aspnetcore.6.6.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swagger/6.6.2/swashbuckle.aspnetcore.swagger.6.6.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggergen/6.6.2/swashbuckle.aspnetcore.swaggergen.6.6.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggerui/6.6.2/swashbuckle.aspnetcore.swaggerui.6.6.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/8.0.0/system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.pipelines/5.0.1/system.io.pipelines.5.0.1.nupkg.sha512"], "logs": []}