﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\Catalog.Application\Catalog.Application.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Update="Data\SeedData\brands.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="Data\SeedData\products.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="Data\SeedData\types.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.2" />
    </ItemGroup>

</Project>
