<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <UserSecretsId>************************************</UserSecretsId>
        <DockerfileContext>..\..\..</DockerfileContext>
        <DockerComposeProjectPath>..\..\..\docker-compose.dcproj</DockerComposeProjectPath>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
        <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1-Preview.1" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\..\..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Catalog.Application\Catalog.Application.csproj" />
      <ProjectReference Include="..\Catalog.Infrastruture\Catalog.Infrastructure.csproj" />
    </ItemGroup>

</Project>
