{"format": 1, "restore": {"/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.API/Catalog.API.csproj": {}}, "projects": {"/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.API/Catalog.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.API/Catalog.API.csproj", "projectName": "Catalog.API", "projectPath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.API/Catalog.API.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.API/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Application/Catalog.Application.csproj": {"projectPath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Application/Catalog.Application.csproj"}, "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Infrastruture/Catalog.Infrastructure.csproj": {"projectPath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Infrastruture/Catalog.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Asp.Versioning.Mvc": {"target": "Package", "version": "[8.1.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.22.1-Preview.1, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Application/Catalog.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Application/Catalog.Application.csproj", "projectName": "Catalog.Application", "projectPath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Application/Catalog.Application.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Application/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Core/Catalog.Core.csproj": {"projectPath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Core/Catalog.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "MediatR": {"target": "Package", "version": "[12.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Core/Catalog.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Core/Catalog.Core.csproj", "projectName": "Catalog.Core", "projectPath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Core/Catalog.Core.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AspNetCore.HealthChecks.MongoDb": {"target": "Package", "version": "[8.0.1, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.27.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Infrastruture/Catalog.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Infrastruture/Catalog.Infrastructure.csproj", "projectName": "Catalog.Infrastructure", "projectPath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Infrastruture/Catalog.Infrastructure.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Infrastruture/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Application/Catalog.Application.csproj": {"projectPath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Application/Catalog.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.101/PortableRuntimeIdentifierGraph.json"}}}}}