﻿"restore":{"projectUniqueName":"/Users/<USER>/Documents/Study/dot-net-core-and-microservices/Ecommerce/Services/Catalog/Catalog.Core/Catalog.Core.csproj","projectName":"Catalog.Core","projectPath":"/Users/<USER>/Documents/Study/dot-net-core-and-microservices/Ecommerce/Services/Catalog/Catalog.Core/Catalog.Core.csproj","outputPath":"/Users/<USER>/Documents/Study/dot-net-core-and-microservices/Ecommerce/Services/Catalog/Catalog.Core/obj/","projectStyle":"PackageReference","originalTargetFrameworks":["net8.0"],"sources":{"/usr/local/share/dotnet/library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0":{"targetAlias":"net8.0","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.100"}"frameworks":{"net8.0":{"targetAlias":"net8.0","dependencies":{"AspNetCore.HealthChecks.MongoDb":{"target":"Package","version":"[8.0.1, )"},"MongoDB.Driver":{"target":"Package","version":"[2.27.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"/usr/local/share/dotnet/sdk/9.0.101/PortableRuntimeIdentifierGraph.json"}}