﻿"restore":{"projectUniqueName":"/Users/<USER>/Documents/Study/dot-net-core-and-microservices/Ecommerce/Services/Catalog/Catalog.Application/Catalog.Application.csproj","projectName":"Catalog.Application","projectPath":"/Users/<USER>/Documents/Study/dot-net-core-and-microservices/Ecommerce/Services/Catalog/Catalog.Application/Catalog.Application.csproj","outputPath":"/Users/<USER>/Documents/Study/dot-net-core-and-microservices/Ecommerce/Services/Catalog/Catalog.Application/obj/","projectStyle":"PackageReference","originalTargetFrameworks":["net8.0"],"sources":{"/usr/local/share/dotnet/library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0":{"targetAlias":"net8.0","projectReferences":{"/Users/<USER>/Documents/Study/dot-net-core-and-microservices/Ecommerce/Services/Catalog/Catalog.Core/Catalog.Core.csproj":{"projectPath":"/Users/<USER>/Documents/Study/dot-net-core-and-microservices/Ecommerce/Services/Catalog/Catalog.Core/Catalog.Core.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.100"}"frameworks":{"net8.0":{"targetAlias":"net8.0","dependencies":{"AutoMapper":{"target":"Package","version":"[13.0.1, )"},"MediatR":{"target":"Package","version":"[12.3.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"/usr/local/share/dotnet/sdk/9.0.101/PortableRuntimeIdentifierGraph.json"}}