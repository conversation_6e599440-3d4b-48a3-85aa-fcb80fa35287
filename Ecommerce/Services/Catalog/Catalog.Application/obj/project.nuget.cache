{"version": 2, "dgSpecHash": "3jZ8HmCO310=", "success": true, "projectFilePath": "/Users/<USER>/Documents/Study/dot-net-core-8-and-microservice/Ecommerce/Services/Catalog/Catalog.Application/Catalog.Application.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/aspnetcore.healthchecks.mongodb/8.0.1/aspnetcore.healthchecks.mongodb.8.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/automapper/13.0.1/automapper.13.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/awssdk.core/3.7.100.14/awssdk.core.3.7.100.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/awssdk.securitytoken/3.7.100.14/awssdk.securitytoken.3.7.100.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/dnsclient/1.6.1/dnsclient.1.6.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/mediatr/12.3.0/mediatr.12.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/mediatr.contracts/2.0.1/mediatr.contracts.2.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/8.0.0/microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/8.0.0/microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.abstractions/8.0.0/microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.healthchecks/8.0.0/microsoft.extensions.diagnostics.healthchecks.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.healthchecks.abstractions/8.0.0/microsoft.extensions.diagnostics.healthchecks.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/8.0.0/microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting.abstractions/8.0.0/microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/8.0.0/microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/8.0.0/microsoft.extensions.options.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/8.0.0/microsoft.extensions.primitives.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.platforms/5.0.0/microsoft.netcore.platforms.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.registry/5.0.0/microsoft.win32.registry.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/mongodb.bson/2.27.0/mongodb.bson.2.27.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/mongodb.driver/2.27.0/mongodb.driver.2.27.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/mongodb.driver.core/2.27.0/mongodb.driver.core.2.27.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/mongodb.libmongocrypt/1.10.0/mongodb.libmongocrypt.1.10.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/sharpcompress/0.30.1/sharpcompress.0.30.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/snappier/1.0.0/snappier.1.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.buffers/4.5.1/system.buffers.4.5.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/8.0.0/system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory/4.5.5/system.memory.4.5.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.compilerservices.unsafe/5.0.0/system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.accesscontrol/5.0.0/system.security.accesscontrol.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.principal.windows/5.0.0/system.security.principal.windows.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/zstdsharp.port/0.7.3/zstdsharp.port.0.7.3.nupkg.sha512"], "logs": []}