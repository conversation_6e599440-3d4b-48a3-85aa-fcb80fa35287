version: '3.8'

services:
  catalogdb:
    container_name: catalogdb
    restart: always
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db


  catalog.api:
    container_name: catalog.api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - "DatabaseSettings__ConnectionString=mongodb://catalogdb:27017"
      - "DatabaseSettings__DatabaseName=Catalogdb"
      - "DatabaseSettings__CollectionName=Products"
      - "DatabaseSettings__CollectionName=Brands"
      - "DatabaseSettings__CollectionName=Types"
    depends_on:
      - catalogdb
    ports:
      - "8000:8080"