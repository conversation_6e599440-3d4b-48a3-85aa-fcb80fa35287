﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36203.30
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{A2D63D6E-6D2C-45DD-A35B-418E3290A9AC}"
	ProjectSection(SolutionItems) = preProject
		types.json = types.json
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Catalog", "Catalog", "{C97896A1-224D-4B96-B1BC-22E680BADE84}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Catalog.API", "Services\Catalog\Catalog.API\Catalog.API.csproj", "{21CCF5D9-4B2C-46B8-88D3-EE78273A2182}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Catalog.Application", "Services\Catalog\Catalog.Application\Catalog.Application.csproj", "{7CBCA3F6-8CD8-4637-A80D-3F5E1B1BD732}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Catalog.Core", "Services\Catalog\Catalog.Core\Catalog.Core.csproj", "{3D8A4E2C-2AF2-4E26-B37E-257FC7F43C55}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Catalog.Infrastructure", "Services\Catalog\Catalog.Infrastruture\Catalog.Infrastructure.csproj", "{7AC20A80-4DC7-4FB0-98FD-2AB4EB5DDFB8}"
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{81DDED9D-158B-E303-5F62-77A2896D2A5A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Basket", "Basket", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Basket.API", "Services\Basket\Basket.API\Basket.API.csproj", "{EF8D3037-251E-4D01-9494-442145878AC3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Basket.Core", "Services\Basket\Basket.Core\Basket.Core.csproj", "{*************-4C57-92CA-B62140EDD86D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Basket.Application", "Services\Basket\Basket.Application\Basket.Application.csproj", "{1075D72E-0D87-4DD6-9D02-D03CCD4E3DC3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Basket.Infrastructure", "Services\Basket\Basket.Infrastructure\Basket.Infrastructure.csproj", "{6D694023-6FC5-48BA-8EEF-2450145E8882}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{21CCF5D9-4B2C-46B8-88D3-EE78273A2182}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{21CCF5D9-4B2C-46B8-88D3-EE78273A2182}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{21CCF5D9-4B2C-46B8-88D3-EE78273A2182}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{21CCF5D9-4B2C-46B8-88D3-EE78273A2182}.Release|Any CPU.Build.0 = Release|Any CPU
		{7CBCA3F6-8CD8-4637-A80D-3F5E1B1BD732}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7CBCA3F6-8CD8-4637-A80D-3F5E1B1BD732}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7CBCA3F6-8CD8-4637-A80D-3F5E1B1BD732}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7CBCA3F6-8CD8-4637-A80D-3F5E1B1BD732}.Release|Any CPU.Build.0 = Release|Any CPU
		{3D8A4E2C-2AF2-4E26-B37E-257FC7F43C55}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3D8A4E2C-2AF2-4E26-B37E-257FC7F43C55}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3D8A4E2C-2AF2-4E26-B37E-257FC7F43C55}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3D8A4E2C-2AF2-4E26-B37E-257FC7F43C55}.Release|Any CPU.Build.0 = Release|Any CPU
		{7AC20A80-4DC7-4FB0-98FD-2AB4EB5DDFB8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7AC20A80-4DC7-4FB0-98FD-2AB4EB5DDFB8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7AC20A80-4DC7-4FB0-98FD-2AB4EB5DDFB8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7AC20A80-4DC7-4FB0-98FD-2AB4EB5DDFB8}.Release|Any CPU.Build.0 = Release|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF8D3037-251E-4D01-9494-442145878AC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF8D3037-251E-4D01-9494-442145878AC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF8D3037-251E-4D01-9494-442145878AC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF8D3037-251E-4D01-9494-442145878AC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-4C57-92CA-B62140EDD86D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-4C57-92CA-B62140EDD86D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-4C57-92CA-B62140EDD86D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-4C57-92CA-B62140EDD86D}.Release|Any CPU.Build.0 = Release|Any CPU
		{1075D72E-0D87-4DD6-9D02-D03CCD4E3DC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1075D72E-0D87-4DD6-9D02-D03CCD4E3DC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1075D72E-0D87-4DD6-9D02-D03CCD4E3DC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1075D72E-0D87-4DD6-9D02-D03CCD4E3DC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{6D694023-6FC5-48BA-8EEF-2450145E8882}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6D694023-6FC5-48BA-8EEF-2450145E8882}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6D694023-6FC5-48BA-8EEF-2450145E8882}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6D694023-6FC5-48BA-8EEF-2450145E8882}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C97896A1-224D-4B96-B1BC-22E680BADE84} = {A2D63D6E-6D2C-45DD-A35B-418E3290A9AC}
		{21CCF5D9-4B2C-46B8-88D3-EE78273A2182} = {C97896A1-224D-4B96-B1BC-22E680BADE84}
		{7CBCA3F6-8CD8-4637-A80D-3F5E1B1BD732} = {C97896A1-224D-4B96-B1BC-22E680BADE84}
		{3D8A4E2C-2AF2-4E26-B37E-257FC7F43C55} = {C97896A1-224D-4B96-B1BC-22E680BADE84}
		{7AC20A80-4DC7-4FB0-98FD-2AB4EB5DDFB8} = {C97896A1-224D-4B96-B1BC-22E680BADE84}
		{02EA681E-C7D8-13C7-8484-4AC65E1B71E8} = {A2D63D6E-6D2C-45DD-A35B-418E3290A9AC}
		{EF8D3037-251E-4D01-9494-442145878AC3} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{*************-4C57-92CA-B62140EDD86D} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{1075D72E-0D87-4DD6-9D02-D03CCD4E3DC3} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{6D694023-6FC5-48BA-8EEF-2450145E8882} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {96AF9E7D-2AEA-48BD-90DC-CD19E3D0D014}
	EndGlobalSection
EndGlobal
